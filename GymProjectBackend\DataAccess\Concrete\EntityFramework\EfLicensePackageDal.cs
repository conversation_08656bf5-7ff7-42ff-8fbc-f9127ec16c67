using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicensePackageDal : EfEntityRepositoryBase<LicensePackage, GymContext>, ILicensePackageDal
    {
        // Constructor injection (Scalability için)
        public EfLicensePackageDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfLicensePackageDal() : base()
        {
        }

        public IResult AddLicensePackage(LicensePackage licensePackage)
        {
            try
            {
                licensePackage.CreationDate = DateTime.Now;
                licensePackage.IsActive = true;

                Add(licensePackage);
                return new SuccessResult("Lisans paketi başarıyla eklendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateLicensePackage(LicensePackage licensePackage)
        {
            try
            {
                licensePackage.UpdatedDate = DateTime.Now;

                Update(licensePackage);
                return new SuccessResult("Lisans paketi başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        // SOLID prensiplerine uygun: Complex business operations DAL'da
        public IResult SoftDeleteLicensePackage(int licensePackageId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var licensePackage = _context.LicensePackages.FirstOrDefault(lp =>
                        lp.LicensePackageID == licensePackageId);

                    if (licensePackage == null)
                    {
                        return new ErrorResult("Lisans paketi bulunamadı.");
                    }

                    // Soft delete işlemi
                    licensePackage.IsActive = false;
                    licensePackage.DeletedDate = DateTime.Now;
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility - using context olmadan
                    using (var context = new GymContext())
                    {
                        var licensePackage = context.LicensePackages.FirstOrDefault(lp =>
                            lp.LicensePackageID == licensePackageId);

                        if (licensePackage == null)
                        {
                            return new ErrorResult("Lisans paketi bulunamadı.");
                        }

                        // Soft delete işlemi
                        licensePackage.IsActive = false;
                        licensePackage.DeletedDate = DateTime.Now;
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Lisans paketi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi silinirken hata oluştu: {ex.Message}");
            }
        }
    }

}
