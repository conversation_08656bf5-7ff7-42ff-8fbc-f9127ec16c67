using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Core.Entities.Concrete;
using System;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUnifiedCompanyDal : IUnifiedCompanyDal
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public EfUnifiedCompanyDal(GymContext context)
        {
            _context = context;
        }

        // Backward compatibility constructor
        public EfUnifiedCompanyDal() : this(new GymContext())
        {
        }

        public IDataResult<string> AddUnifiedCompanyWithAllEntities(UnifiedCompanyAddDto unifiedCompanyDto)
        {
            try
            {
                // 1. User oluştur (Adım 1'den gelen bilgiler)
                // Geçici şifre oluştur (telefon numarasının son 4 hanesi)
                string tempPassword = "temp1234"; // Geçici şifre, sonra telefon numarasından güncellenecek
                if (!string.IsNullOrEmpty(unifiedCompanyDto.OwnerPhone) && unifiedCompanyDto.OwnerPhone.Length >= 4)
                {
                    tempPassword = unifiedCompanyDto.OwnerPhone.Substring(unifiedCompanyDto.OwnerPhone.Length - 4);
                }

                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

                var user = new User
                {
                    FirstName = unifiedCompanyDto.UserFirstName,
                    LastName = unifiedCompanyDto.UserLastName,
                    Email = unifiedCompanyDto.UserEmail,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = true, // Telefon numarasından oluşturulan şifre değiştirilmeli
                    CreationDate = DateTime.Now
                };

                _context.Users.Add(user);
                _context.SaveChanges();

                // 2. Company oluştur (Adım 2'den gelen bilgiler)
                var company = new Company
                {
                    CompanyName = unifiedCompanyDto.CompanyName,
                    PhoneNumber = unifiedCompanyDto.CompanyPhone,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.Companies.Add(company);
                _context.SaveChanges();

                // 3. CompanyAdress oluştur (Adım 3'ten gelen bilgiler)
                var companyAddress = new CompanyAdress
                {
                    CompanyID = company.CompanyID,
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Adress = unifiedCompanyDto.Address,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.CompanyAdresses.Add(companyAddress);
                _context.SaveChanges();

                // 4. CompanyUser oluştur (Adım 4'ten gelen bilgiler)
                var companyUser = new CompanyUser
                {
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Name = unifiedCompanyDto.OwnerFullName,
                    PhoneNumber = unifiedCompanyDto.OwnerPhone,
                    Email = unifiedCompanyDto.UserEmail, // Adım 1'den gelen email
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.CompanyUsers.Add(companyUser);
                _context.SaveChanges();

                // 5. UserCompany ilişkisi oluştur
                var userCompany = new UserCompany
                {
                    UserID = companyUser.CompanyUserID, // CompanyUser ID'si UserCompany'de UserID olarak kullanılıyor
                    CompanyId = company.CompanyID,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.UserCompanies.Add(userCompany);
                _context.SaveChanges();

                // Başarılı sonuç döndür - TransactionScopeAspect transaction'ı yönetiyor
                return new SuccessDataResult<string>(tempPassword, $"Salon başarıyla oluşturuldu. Geçici şifre: {tempPassword}");
            }
            catch (Exception ex)
            {
                // Hata durumunda TransactionScopeAspect rollback yapacak
                return new ErrorDataResult<string>("Salon oluşturulurken bir hata oluştu: " + ex.Message);
            }
        }
    }
}
